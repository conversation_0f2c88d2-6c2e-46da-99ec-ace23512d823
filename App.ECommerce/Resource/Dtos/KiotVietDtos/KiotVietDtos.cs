using Newtonsoft.Json;

using System;
using System.Collections.Generic;

namespace App.ECommerce.Resource.Dtos.KiotVietDtos
{
    public class KiotVietTokenResponseDto
    {
        public string access_token { get; set; }
        public string token_type { get; set; }
        public int expires_in { get; set; }
        public string refresh_token { get; set; }
        public string scope { get; set; }
        public DateTime? expires_at { get; set; } // Thời điểm hết hạn (tính toán từ expires_in)

        // Constructor để tự động tính expires_at
        public KiotVietTokenResponseDto()
        {
            if (expires_in > 0)
            {
                expires_at = DateTime.UtcNow.AddSeconds(expires_in);
            }
        }
    }

    public class KiotVietProductWebhookDto
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string? Description { get; set; }
        public decimal BasePrice { get; set; }
        public int? CategoryId { get; set; }
        public string CategoryName { get; set; }
        public bool IsActive { get; set; }

        [JsonProperty(nameof(Images))]
        public List<string>? Images { get; set; }

        public DateTime ModifiedDate { get; set; }
    }

    public class KiotVietOrderWebhookDto
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public int CustomerId { get; set; }
        public string CustomerCode { get; set; }
        public string CustomerName { get; set; }


        public decimal Total { get; set; }
        public string Status { get; set; }
        public string Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public List<KiotVietOrderDetailDto> OrderDetails { get; set; }
    }

    public class KiotVietOrderDetailDto
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public int CustomerId { get; set; }
        public decimal Total { get; set; }
        public string Status { get; set; }
        public string Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public List<KiotVietOrderDetailDto> OrderDetails { get; set; }
        public int? ProductId { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public decimal Discount { get; set; }
    }

    public class KiotVietCustomerWebhookDto
    {
        public long Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public bool? Gender { get; set; }
        public DateTime? BirthDate { get; set; }
        public string ContactNumber { get; set; }
        public string Address { get; set; }
        public string LocationName { get; set; }
        public string Email { get; set; }
        public DateTime ModifiedDate { get; set; }
        public byte? Type { get; set; }
        public string Organization { get; set; }
        public string TaxCode { get; set; }
        public string Comments { get; set; }
    }

    public class KiotVietCustomerDto
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string ContactNumber { get; set; }
        public string Address { get; set; }
        public string LocationName { get; set; }
        public string WardName { get; set; }

    }

    public class KiotVietCreateOrderResponseDto
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Message { get; set; }
    }

    public class KiotVietWebhookEventDto
    {
        public string Type { get; set; }
        public object Data { get; set; }
        public DateTime CreatedDate { get; set; }
        public string RetailerId { get; set; }
    }

    public class KiotVietWebhookDto
    {
        [JsonProperty("id")]
        public long Id { get; set; } // Changed from int to long

        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonProperty("isActive")]
        public bool IsActive { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("createdDate")]
        public DateTime CreatedDate { get; set; }

        [JsonProperty("modifiedDate")]
        public DateTime? ModifiedDate { get; set; }
    }

    public class KiotVietWebhookListResponseDto
    {
        [JsonProperty("data")]
        public List<KiotVietWebhookDto> Data { get; set; } = new();

        [JsonProperty("pageSize")]
        public int PageSize { get; set; }

        [JsonProperty("pageIndex")]
        public int PageIndex { get; set; }

        [JsonProperty("total")]
        public int Total { get; set; }
    }

    public class KiotVietWebhookCreateResponseDto
    {
        [JsonProperty("id")]
        public long Id { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonProperty("isActive")]
        public bool IsActive { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("createdDate")]
        public DateTime CreatedDate { get; set; }
    }

    public class KiotVietWebhookCreateRequestDto
    {
        [JsonProperty("webhook")]
        public KiotVietWebhookRegistrationDto Webhook { get; set; }
    }

    public class KiotVietWebhookRegistrationDto
    {
        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonProperty("isActive")]
        public bool IsActive { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }
    }

    public class KiotVietStockWebhookDto
    {
        public int ProductId { get; set; }
        public int BranchId { get; set; }
        public int OnHand { get; set; }
        public int Reserved { get; set; }
        public DateTime ModifiedDate { get; set; }
    }

    public class KiotVietCategoryWebhookDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public int? ParentId { get; set; }
        public bool IsActive { get; set; }
        public DateTime ModifiedDate { get; set; }
    }

    public class KiotVietWebhookPayloadDto
    {
        public string Id { get; set; }
        public int Attempt { get; set; }
        public List<KiotVietNotificationDto> Notifications { get; set; }
    }

    public class KiotVietNotificationDto
    {
        public string Action { get; set; }
        public List<object> Data { get; set; }
    }
}